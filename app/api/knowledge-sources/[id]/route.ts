import { NextRequest, NextResponse } from 'next/server';
import { KnowledgeSourcesDB } from '@/lib/database';
import { supabase } from '@/lib/supabase';

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const { id } = await params;
  try {
    // Get the auth token from the Authorization header
    const authHeader = request.headers.get('Authorization');
    const token = authHeader?.split(' ')[1];

    if (!token) {
      return NextResponse.json({ error: 'No token provided' }, { status: 401 });
    }

    // Verify the token and get the user
    const { data: { user }, error: authError } = await supabase.auth.getUser(token);

    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const updates = await request.json();
    const updated = await KnowledgeSourcesDB.update(id, updates, user.id);
    
    return NextResponse.json({ source: updated });
  } catch (error) {
    console.error('Error updating knowledge source:', error);
    return NextResponse.json({ error: 'Failed to update knowledge source' }, { status: 500 });
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const { id } = await params;
  try {
    // Get the auth token from the Authorization header
    const authHeader = request.headers.get('Authorization');
    const token = authHeader?.split(' ')[1];

    if (!token) {
      return NextResponse.json({ error: 'No token provided' }, { status: 401 });
    }

    // Verify the token and get the user
    const { data: { user }, error: authError } = await supabase.auth.getUser(token);

    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    await KnowledgeSourcesDB.delete(id, user.id);
    
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error deleting knowledge source:', error);
    return NextResponse.json({ error: 'Failed to delete knowledge source' }, { status: 500 });
  }
}